<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xy.admin.mapper.ApiOrderMapper">
  <resultMap id="BaseResultMap" type="com.xy.admin.entity.ApiOrder">
    <!--@mbg.generated-->
    <!--@Table api_order-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="channel_no" jdbcType="VARCHAR" property="channelNo" />
    <result column="product_no" jdbcType="VARCHAR" property="productNo" />
    <result column="mobile_no" jdbcType="VARCHAR" property="mobileNo" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="order_status" jdbcType="VARCHAR" property="orderStatus" />
    <result column="out_order_status" jdbcType="VARCHAR" property="outOrderStatus" />
    <result column="out_order_id" jdbcType="VARCHAR" property="outOrderId" />
    <result column="operation_type" jdbcType="VARCHAR" property="operationType" />
    <result column="client_ip" jdbcType="VARCHAR" property="clientIp" />
    <result column="user_agent" jdbcType="VARCHAR" property="userAgent" />
    <result column="app_package" jdbcType="VARCHAR" property="appPackage" />
    <result column="app_name" jdbcType="VARCHAR" property="appName" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="page_url" jdbcType="VARCHAR" property="pageUrl" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="server" jdbcType="VARCHAR" property="server" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, app_id, channel_no, product_no, mobile_no, order_id, order_status, out_order_status, 
    out_order_id, operation_type, client_ip, user_agent, app_package, app_name, platform, 
    page_url, remark, create_time, update_time, server
  </sql>
  <update id="updateByPrimaryKey" parameterType="com.xy.admin.entity.ApiOrder">
    <!--@mbg.generated-->
    update api_order
    set app_id = #{appId,jdbcType=VARCHAR},
      channel_no = #{channelNo,jdbcType=VARCHAR},
      product_no = #{productNo,jdbcType=VARCHAR},
      mobile_no = #{mobileNo,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=VARCHAR},
      out_order_status = #{outOrderStatus,jdbcType=VARCHAR},
      out_order_id = #{outOrderId,jdbcType=VARCHAR},
      operation_type = #{operationType,jdbcType=VARCHAR},
      client_ip = #{clientIp,jdbcType=VARCHAR},
      user_agent = #{userAgent,jdbcType=VARCHAR},
      app_package = #{appPackage,jdbcType=VARCHAR},
      app_name = #{appName,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=VARCHAR},
      page_url = #{pageUrl,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      server = #{server,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 多维度统计查询 -->
  <select id="getMultiDimensionStatistics" resultType="java.util.Map">
    SELECT
    <choose>
      <when test="dimension == 'CHANNEL'">
        channel_no as dimensionValue,
        channel_no as dimensionName,
      </when>
      <when test="dimension == 'PRODUCT'">
        product_no as dimensionValue,
        product_no as dimensionName,
      </when>
      <when test="dimension == 'APP'">
        app_name as dimensionValue,
        app_name as dimensionName,
      </when>
      <when test="dimension == 'PLATFORM'">
        platform as dimensionValue,
        platform as dimensionName,
      </when>
      <when test="dimension == 'OPERATION'">
        operation_type as dimensionValue,
        operation_type as dimensionName,
      </when>
      <when test="dimension == 'TIME'">
        DATE(create_time) as dimensionValue,
        DATE(create_time) as dimensionName,
      </when>
      <otherwise>
        'ALL' as dimensionValue,
        'ALL' as dimensionName,
      </otherwise>
    </choose>
    COUNT(*) as totalCount,
    COUNT(CASE WHEN operation_type = 'SMS' THEN 1 END) as smsCount,
    COUNT(CASE WHEN operation_type = 'ORDER' THEN 1 END) as orderCount,
    <if test="distinctUsers != null and distinctUsers">
      COUNT(DISTINCT mobile_no) as distinctUserCount,
    </if>
    <if test="includeSuccessData != null and includeSuccessData">
      (SELECT COUNT(*) FROM pro_order po WHERE po.order_id = ao.order_id AND po.report_status = 1) as successCount,
    </if>
    ROUND(
      CASE
        WHEN COUNT(CASE WHEN operation_type = 'SMS' THEN 1 END) > 0
        THEN COUNT(CASE WHEN operation_type = 'ORDER' THEN 1 END) * 100.0 / COUNT(CASE WHEN operation_type = 'SMS' THEN 1 END)
        ELSE 0
      END, 2
    ) as submitRate
    FROM api_order ao
    <where>
      <if test="startTime != null">
        AND create_time >= #{startTime}
      </if>
      <if test="endTime != null">
        AND create_time &lt;= #{endTime}
      </if>
      <if test="appIds != null and appIds.size() > 0">
        AND app_id IN
        <foreach collection="appIds" item="appId" open="(" separator="," close=")">
          #{appId}
        </foreach>
      </if>
      <if test="channelNos != null and channelNos.size() > 0">
        AND channel_no IN
        <foreach collection="channelNos" item="channelNo" open="(" separator="," close=")">
          #{channelNo}
        </foreach>
      </if>
      <if test="productNos != null and productNos.size() > 0">
        AND product_no IN
        <foreach collection="productNos" item="productNo" open="(" separator="," close=")">
          #{productNo}
        </foreach>
      </if>
      <if test="operationTypes != null and operationTypes.size() > 0">
        AND operation_type IN
        <foreach collection="operationTypes" item="operationType" open="(" separator="," close=")">
          #{operationType}
        </foreach>
      </if>
      <if test="platforms != null and platforms.size() > 0">
        AND platform IN
        <foreach collection="platforms" item="platform" open="(" separator="," close=")">
          #{platform}
        </foreach>
      </if>
      <if test="appPackages != null and appPackages.size() > 0">
        AND app_package IN
        <foreach collection="appPackages" item="appPackage" open="(" separator="," close=")">
          #{appPackage}
        </foreach>
      </if>
      <if test="appNames != null and appNames.size() > 0">
        AND app_name IN
        <foreach collection="appNames" item="appName" open="(" separator="," close=")">
          #{appName}
        </foreach>
      </if>
      <if test="servers != null and servers.size() > 0">
        AND server IN
        <foreach collection="servers" item="server" open="(" separator="," close=")">
          #{server}
        </foreach>
      </if>
      <if test="orderStatuses != null and orderStatuses.size() > 0">
        AND order_status IN
        <foreach collection="orderStatuses" item="orderStatus" open="(" separator="," close=")">
          #{orderStatus}
        </foreach>
      </if>
      <if test="outOrderStatuses != null and outOrderStatuses.size() > 0">
        AND out_order_status IN
        <foreach collection="outOrderStatuses" item="outOrderStatus" open="(" separator="," close=")">
          #{outOrderStatus}
        </foreach>
      </if>
    </where>
    <if test="dimension != 'ALL'">
      GROUP BY
      <choose>
        <when test="dimension == 'CHANNEL'">channel_no</when>
        <when test="dimension == 'PRODUCT'">product_no</when>
        <when test="dimension == 'APP'">app_name</when>
        <when test="dimension == 'PLATFORM'">platform</when>
        <when test="dimension == 'OPERATION'">operation_type</when>
        <when test="dimension == 'TIME'">DATE(create_time)</when>
      </choose>
    </if>
    <if test="minCount != null and minCount > 0">
      HAVING COUNT(*) >= #{minCount}
    </if>
    <if test="sortField != null and sortDirection != null">
      ORDER BY ${sortField} ${sortDirection}
    </if>
  </select>

  <!-- 时间趋势统计查询 -->
  <select id="getTimeTrendStatistics" resultType="java.util.Map">
    SELECT
    <choose>
      <when test="timeGranularity == 'DAY'">
        DATE(create_time) as statisticsDate,
        DATE_FORMAT(create_time, '%Y-%m-%d') as timeLabel,
      </when>
      <when test="timeGranularity == 'WEEK'">
        DATE(DATE_SUB(create_time, INTERVAL WEEKDAY(create_time) DAY)) as statisticsDate,
        CONCAT(DATE_FORMAT(DATE_SUB(create_time, INTERVAL WEEKDAY(create_time) DAY), '%Y-%m-%d'), ' ~ ',
               DATE_FORMAT(DATE_ADD(DATE_SUB(create_time, INTERVAL WEEKDAY(create_time) DAY), INTERVAL 6 DAY), '%Y-%m-%d')) as timeLabel,
      </when>
      <when test="timeGranularity == 'MONTH'">
        DATE_FORMAT(create_time, '%Y-%m-01') as statisticsDate,
        DATE_FORMAT(create_time, '%Y-%m') as timeLabel,
      </when>
      <otherwise>
        DATE(create_time) as statisticsDate,
        DATE_FORMAT(create_time, '%Y-%m-%d') as timeLabel,
      </otherwise>
    </choose>
    COUNT(*) as totalCount,
    COUNT(CASE WHEN operation_type = 'SMS' THEN 1 END) as smsCount,
    COUNT(CASE WHEN operation_type = 'ORDER' THEN 1 END) as orderCount,
    COUNT(DISTINCT mobile_no) as distinctUserCount,
    <if test="includeSuccessData != null and includeSuccessData">
      (SELECT COUNT(*) FROM pro_order po WHERE po.order_id IN (
        SELECT DISTINCT order_id FROM api_order ao2
        WHERE ao2.create_time >= #{startTime} AND ao2.create_time &lt;= #{endTime}
        AND po.report_status = 1
      )) as successCount,
    </if>
    ROUND(
      CASE
        WHEN COUNT(CASE WHEN operation_type = 'SMS' THEN 1 END) > 0
        THEN COUNT(CASE WHEN operation_type = 'ORDER' THEN 1 END) * 100.0 / COUNT(CASE WHEN operation_type = 'SMS' THEN 1 END)
        ELSE 0
      END, 2
    ) as submitRate
    FROM api_order
    <where>
      <if test="startTime != null">
        AND create_time >= #{startTime}
      </if>
      <if test="endTime != null">
        AND create_time &lt;= #{endTime}
      </if>
      <if test="channelNos != null and channelNos.size() > 0">
        AND channel_no IN
        <foreach collection="channelNos" item="channelNo" open="(" separator="," close=")">
          #{channelNo}
        </foreach>
      </if>
      <if test="productNos != null and productNos.size() > 0">
        AND product_no IN
        <foreach collection="productNos" item="productNo" open="(" separator="," close=")">
          #{productNo}
        </foreach>
      </if>
      <if test="operationTypes != null and operationTypes.size() > 0">
        AND operation_type IN
        <foreach collection="operationTypes" item="operationType" open="(" separator="," close=")">
          #{operationType}
        </foreach>
      </if>
    </where>
    GROUP BY
    <choose>
      <when test="timeGranularity == 'DAY'">DATE(create_time)</when>
      <when test="timeGranularity == 'WEEK'">DATE(DATE_SUB(create_time, INTERVAL WEEKDAY(create_time) DAY))</when>
      <when test="timeGranularity == 'MONTH'">DATE_FORMAT(create_time, '%Y-%m-01')</when>
      <otherwise>DATE(create_time)</otherwise>
    </choose>
    ORDER BY statisticsDate ASC
  </select>

  <!-- 排行榜统计查询 -->
  <select id="getRankingStatistics" resultType="java.util.Map">
    SELECT
    <choose>
      <when test="rankingType == 'CHANNEL'">
        channel_no as dimensionValue,
        channel_no as dimensionName,
      </when>
      <when test="rankingType == 'PRODUCT'">
        product_no as dimensionValue,
        product_no as dimensionName,
      </when>
      <when test="rankingType == 'APP'">
        app_name as dimensionValue,
        app_name as dimensionName,
      </when>
      <when test="rankingType == 'PLATFORM'">
        platform as dimensionValue,
        platform as dimensionName,
      </when>
    </choose>
    COUNT(*) as totalCount,
    COUNT(CASE WHEN operation_type = 'SMS' THEN 1 END) as smsCount,
    COUNT(CASE WHEN operation_type = 'ORDER' THEN 1 END) as orderCount,
    COUNT(DISTINCT mobile_no) as distinctUserCount,
    <if test="includeSuccessData != null and includeSuccessData">
      (SELECT COUNT(*) FROM pro_order po WHERE po.order_id = ao.order_id AND po.report_status = 1) as successCount,
    </if>
    ROUND(
      CASE
        WHEN COUNT(CASE WHEN operation_type = 'SMS' THEN 1 END) > 0
        THEN COUNT(CASE WHEN operation_type = 'ORDER' THEN 1 END) * 100.0 / COUNT(CASE WHEN operation_type = 'SMS' THEN 1 END)
        ELSE 0
      END, 2
    ) as submitRate,
    ROUND(
      <choose>
        <when test="sortMetric == 'TOTAL'">COUNT(*)</when>
        <when test="sortMetric == 'SMS'">COUNT(CASE WHEN operation_type = 'SMS' THEN 1 END)</when>
        <when test="sortMetric == 'ORDER'">COUNT(CASE WHEN operation_type = 'ORDER' THEN 1 END)</when>
        <when test="sortMetric == 'SUCCESS'">
          <if test="includeSuccessData != null and includeSuccessData">
            (SELECT COUNT(*) FROM pro_order po WHERE po.order_id = ao.order_id AND po.report_status = 1)
          </if>
          <if test="includeSuccessData == null or !includeSuccessData">0</if>
        </when>
        <when test="sortMetric == 'SUBMIT_RATE'">
          CASE
            WHEN COUNT(CASE WHEN operation_type = 'SMS' THEN 1 END) > 0
            THEN COUNT(CASE WHEN operation_type = 'ORDER' THEN 1 END) * 100.0 / COUNT(CASE WHEN operation_type = 'SMS' THEN 1 END)
            ELSE 0
          END
        </when>
        <otherwise>COUNT(*)</otherwise>
      </choose>
      , 2
    ) as sortValue
    FROM api_order ao
    <where>
      <if test="startTime != null">
        AND create_time >= #{startTime}
      </if>
      <if test="endTime != null">
        AND create_time &lt;= #{endTime}
      </if>
      <if test="channelNos != null and channelNos.size() > 0">
        AND channel_no IN
        <foreach collection="channelNos" item="channelNo" open="(" separator="," close=")">
          #{channelNo}
        </foreach>
      </if>
      <if test="productNos != null and productNos.size() > 0">
        AND product_no IN
        <foreach collection="productNos" item="productNo" open="(" separator="," close=")">
          #{productNo}
        </foreach>
      </if>
      <if test="operationTypes != null and operationTypes.size() > 0">
        AND operation_type IN
        <foreach collection="operationTypes" item="operationType" open="(" separator="," close=")">
          #{operationType}
        </foreach>
      </if>
      <if test="platforms != null and platforms.size() > 0">
        AND platform IN
        <foreach collection="platforms" item="platform" open="(" separator="," close=")">
          #{platform}
        </foreach>
      </if>
    </where>
    GROUP BY
    <choose>
      <when test="rankingType == 'CHANNEL'">channel_no</when>
      <when test="rankingType == 'PRODUCT'">product_no</when>
      <when test="rankingType == 'APP'">app_name</when>
      <when test="rankingType == 'PLATFORM'">platform</when>
    </choose>
    ORDER BY sortValue DESC
    <if test="topN != null and topN > 0">
      LIMIT #{topN}
    </if>
  </select>

  <!-- 实时统计概览 -->
  <select id="getRealtimeOverview" resultType="java.util.Map">
    SELECT
      COUNT(*) as totalCount,
      COUNT(CASE WHEN operation_type = 'SMS' THEN 1 END) as smsCount,
      COUNT(CASE WHEN operation_type = 'ORDER' THEN 1 END) as orderCount,
      COUNT(DISTINCT mobile_no) as distinctUserCount,
      COUNT(DISTINCT channel_no) as channelCount,
      COUNT(DISTINCT product_no) as productCount,
      COUNT(DISTINCT platform) as platformCount,
      ROUND(
        CASE
          WHEN COUNT(CASE WHEN operation_type = 'SMS' THEN 1 END) > 0
          THEN COUNT(CASE WHEN operation_type = 'ORDER' THEN 1 END) * 100.0 / COUNT(CASE WHEN operation_type = 'SMS' THEN 1 END)
          ELSE 0
        END, 2
      ) as submitRate
    FROM api_order
    <where>
      <if test="startTime != null">
        AND create_time >= #{startTime}
      </if>
      <if test="endTime != null">
        AND create_time &lt;= #{endTime}
      </if>
    </where>
  </select>

  <!-- 转化漏斗统计 -->
  <select id="getConversionFunnelStatistics" resultType="java.util.Map">
    SELECT
      'SMS发送' as stepName,
      1 as stepOrder,
      COUNT(CASE WHEN operation_type = 'SMS' THEN 1 END) as stepCount,
      100.0 as conversionRate
    FROM api_order
    <where>
      <if test="startTime != null">
        AND create_time >= #{startTime}
      </if>
      <if test="endTime != null">
        AND create_time &lt;= #{endTime}
      </if>
      <if test="channelNos != null and channelNos.size() > 0">
        AND channel_no IN
        <foreach collection="channelNos" item="channelNo" open="(" separator="," close=")">
          #{channelNo}
        </foreach>
      </if>
      <if test="productNos != null and productNos.size() > 0">
        AND product_no IN
        <foreach collection="productNos" item="productNo" open="(" separator="," close=")">
          #{productNo}
        </foreach>
      </if>
    </where>

    UNION ALL

    SELECT
      '订单提交' as stepName,
      2 as stepOrder,
      COUNT(CASE WHEN operation_type = 'ORDER' THEN 1 END) as stepCount,
      ROUND(
        CASE
          WHEN COUNT(CASE WHEN operation_type = 'SMS' THEN 1 END) > 0
          THEN COUNT(CASE WHEN operation_type = 'ORDER' THEN 1 END) * 100.0 / COUNT(CASE WHEN operation_type = 'SMS' THEN 1 END)
          ELSE 0
        END, 2
      ) as conversionRate
    FROM api_order
    <where>
      <if test="startTime != null">
        AND create_time >= #{startTime}
      </if>
      <if test="endTime != null">
        AND create_time &lt;= #{endTime}
      </if>
      <if test="channelNos != null and channelNos.size() > 0">
        AND channel_no IN
        <foreach collection="channelNos" item="channelNo" open="(" separator="," close=")">
          #{channelNo}
        </foreach>
      </if>
      <if test="productNos != null and productNos.size() > 0">
        AND product_no IN
        <foreach collection="productNos" item="productNo" open="(" separator="," close=")">
          #{productNo}
        </foreach>
      </if>
    </where>

    UNION ALL

    SELECT
      '办理成功' as stepName,
      3 as stepOrder,
      (SELECT COUNT(*) FROM pro_order po
       WHERE po.report_status = 1
       AND po.order_id IN (
         SELECT DISTINCT order_id FROM api_order ao
         <where>
           <if test="startTime != null">
             AND ao.create_time >= #{startTime}
           </if>
           <if test="endTime != null">
             AND ao.create_time &lt;= #{endTime}
           </if>
           <if test="channelNos != null and channelNos.size() > 0">
             AND ao.channel_no IN
             <foreach collection="channelNos" item="channelNo" open="(" separator="," close=")">
               #{channelNo}
             </foreach>
           </if>
           <if test="productNos != null and productNos.size() > 0">
             AND ao.product_no IN
             <foreach collection="productNos" item="productNo" open="(" separator="," close=")">
               #{productNo}
             </foreach>
           </if>
         </where>
       )
      ) as stepCount,
      ROUND(
        CASE
          WHEN COUNT(CASE WHEN operation_type = 'ORDER' THEN 1 END) > 0
          THEN (SELECT COUNT(*) FROM pro_order po
                WHERE po.report_status = 1
                AND po.order_id IN (SELECT DISTINCT order_id FROM api_order)) * 100.0 / COUNT(CASE WHEN operation_type = 'ORDER' THEN 1 END)
          ELSE 0
        END, 2
      ) as conversionRate
    FROM api_order
    <where>
      <if test="startTime != null">
        AND create_time >= #{startTime}
      </if>
      <if test="endTime != null">
        AND create_time &lt;= #{endTime}
      </if>
      <if test="channelNos != null and channelNos.size() > 0">
        AND channel_no IN
        <foreach collection="channelNos" item="channelNo" open="(" separator="," close=")">
          #{channelNo}
        </foreach>
      </if>
      <if test="productNos != null and productNos.size() > 0">
        AND product_no IN
        <foreach collection="productNos" item="productNo" open="(" separator="," close=")">
          #{productNo}
        </foreach>
      </if>
    </where>

    ORDER BY stepOrder
  </select>

  <!-- 异常数据监控 -->
  <select id="getAnomalyStatistics" resultType="java.util.Map">
    SELECT
      'order_status_anomaly' as anomalyType,
      '异常订单状态' as anomalyName,
      order_status as anomalyValue,
      COUNT(*) as anomalyCount,
      ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM api_order WHERE create_time >= #{startTime} AND create_time &lt;= #{endTime}), 2) as anomalyRate
    FROM api_order
    <where>
      <if test="startTime != null">
        AND create_time >= #{startTime}
      </if>
      <if test="endTime != null">
        AND create_time &lt;= #{endTime}
      </if>
      AND (order_status IS NULL OR order_status = '' OR order_status NOT IN ('SUCCESS', 'FAILED', 'PENDING'))
    </where>
    GROUP BY order_status

    UNION ALL

    SELECT
      'out_order_status_anomaly' as anomalyType,
      '异常外部订单状态' as anomalyName,
      out_order_status as anomalyValue,
      COUNT(*) as anomalyCount,
      ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM api_order WHERE create_time >= #{startTime} AND create_time &lt;= #{endTime}), 2) as anomalyRate
    FROM api_order
    <where>
      <if test="startTime != null">
        AND create_time >= #{startTime}
      </if>
      <if test="endTime != null">
        AND create_time &lt;= #{endTime}
      </if>
      AND (out_order_status IS NULL OR out_order_status = '')
    </where>
    GROUP BY out_order_status

    UNION ALL

    SELECT
      'missing_data_anomaly' as anomalyType,
      '缺失数据异常' as anomalyName,
      CASE
        WHEN mobile_no IS NULL OR mobile_no = '' THEN 'missing_mobile'
        WHEN channel_no IS NULL OR channel_no = '' THEN 'missing_channel'
        WHEN product_no IS NULL OR product_no = '' THEN 'missing_product'
        ELSE 'other_missing'
      END as anomalyValue,
      COUNT(*) as anomalyCount,
      ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM api_order WHERE create_time >= #{startTime} AND create_time &lt;= #{endTime}), 2) as anomalyRate
    FROM api_order
    <where>
      <if test="startTime != null">
        AND create_time >= #{startTime}
      </if>
      <if test="endTime != null">
        AND create_time &lt;= #{endTime}
      </if>
      AND (mobile_no IS NULL OR mobile_no = '' OR channel_no IS NULL OR channel_no = '' OR product_no IS NULL OR product_no = '')
    </where>
    GROUP BY
      CASE
        WHEN mobile_no IS NULL OR mobile_no = '' THEN 'missing_mobile'
        WHEN channel_no IS NULL OR channel_no = '' THEN 'missing_channel'
        WHEN product_no IS NULL OR product_no = '' THEN 'missing_product'
        ELSE 'other_missing'
      END

    ORDER BY anomalyCount DESC
  </select>

</mapper>