package com.xy.admin.vo.statistics;

import com.xy.base.starter.dto.CommonPageQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 统计分析查询VO
 * 
 * <AUTHOR>
 * @since 2025/8/7
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class StatisticsQueryVO extends CommonPageQuery {
    private static final long serialVersionUID = 1L;

    /**
     * 统计维度类型：
     * CHANNEL - 渠道维度
     * PRODUCT - 产品维度
     * TIME - 时间维度
     * APP - 应用维度
     * PLATFORM - 平台维度
     * OPERATION - 操作类型维度
     * CROSS - 交叉分析维度
     */
    @NotNull(message = "统计维度不能为空")
    private String dimension;

    /**
     * 时间粒度：DAY-日，WEEK-周，MONTH-月
     */
    private String timeGranularity;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 开始日期（用于按日期统计）
     */
    private LocalDate startDate;

    /**
     * 结束日期（用于按日期统计）
     */
    private LocalDate endDate;

    /**
     * 应用ID列表
     */
    private List<String> appIds;

    /**
     * 渠道编号列表
     */
    private List<String> channelNos;

    /**
     * 产品编号列表
     */
    private List<String> productNos;

    /**
     * 操作类型列表：SMS-短信发送，ORDER-订单提交
     */
    private List<String> operationTypes;

    /**
     * 平台名称列表
     */
    private List<String> platforms;

    /**
     * 应用包名列表
     */
    private List<String> appPackages;

    /**
     * 应用名称列表
     */
    private List<String> appNames;

    /**
     * 服务器标识列表
     */
    private List<String> servers;

    /**
     * 订单状态码列表
     */
    private List<String> orderStatuses;

    /**
     * 外部订单状态列表
     */
    private List<String> outOrderStatuses;

    /**
     * 是否包含成功数据（需要关联pro_order表）
     */
    private Boolean includeSuccessData;

    /**
     * 排序字段
     */
    private String sortField;

    /**
     * 排序方向：ASC-升序，DESC-降序
     */
    private String sortDirection;

    /**
     * 分组字段列表（用于交叉分析）
     */
    private List<String> groupFields;

    /**
     * 是否只统计去重用户数
     */
    private Boolean distinctUsers;

    /**
     * 最小统计值过滤（用于过滤小数据）
     */
    private Integer minCount;
}
