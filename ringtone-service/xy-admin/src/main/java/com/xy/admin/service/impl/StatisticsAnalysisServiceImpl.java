package com.xy.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xy.admin.mapper.ApiOrderMapper;
import com.xy.admin.service.StatisticsAnalysisService;
import com.xy.admin.vo.statistics.RankingVO;
import com.xy.admin.vo.statistics.StatisticsQueryVO;
import com.xy.admin.vo.statistics.StatisticsResultVO;
import com.xy.admin.vo.statistics.TrendAnalysisVO;
import com.xy.base.core.util.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 统计分析服务实现类
 * 
 * <AUTHOR>
 * @since 2025/8/7
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsAnalysisServiceImpl implements StatisticsAnalysisService {

    private final ApiOrderMapper apiOrderMapper;

    private static final String CACHE_PREFIX = "statistics:";
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public StatisticsResultVO getMultiDimensionStatistics(StatisticsQueryVO queryVO) {
        log.info("执行多维度统计分析，维度：{}", queryVO.getDimension());
        
        List<Map<String, Object>> rawData = apiOrderMapper.getMultiDimensionStatistics(
                queryVO.getDimension(),
                queryVO.getStartTime(),
                queryVO.getEndTime(),
                queryVO.getAppIds(),
                queryVO.getChannelNos(),
                queryVO.getProductNos(),
                queryVO.getOperationTypes(),
                queryVO.getPlatforms(),
                queryVO.getAppPackages(),
                queryVO.getAppNames(),
                queryVO.getServers(),
                queryVO.getOrderStatuses(),
                queryVO.getOutOrderStatuses(),
                queryVO.getIncludeSuccessData(),
                queryVO.getGroupFields(),
                queryVO.getSortField(),
                queryVO.getSortDirection(),
                queryVO.getDistinctUsers(),
                queryVO.getMinCount()
        );

        return buildStatisticsResult(queryVO.getDimension(), rawData);
    }

    @Override
    public StatisticsResultVO getChannelEffectAnalysis(StatisticsQueryVO queryVO) {
        log.info("执行渠道效果分析");
        queryVO.setDimension("CHANNEL");
        queryVO.setIncludeSuccessData(true);
        return getMultiDimensionStatistics(queryVO);
    }

    @Override
    public StatisticsResultVO getProductPerformanceAnalysis(StatisticsQueryVO queryVO) {
        log.info("执行产品表现分析");
        queryVO.setDimension("PRODUCT");
        queryVO.setIncludeSuccessData(true);
        return getMultiDimensionStatistics(queryVO);
    }

    @Override
    public TrendAnalysisVO getTimeTrendAnalysis(StatisticsQueryVO queryVO) {
        log.info("执行时间趋势分析，时间粒度：{}", queryVO.getTimeGranularity());
        
        List<Map<String, Object>> rawData = apiOrderMapper.getTimeTrendStatistics(
                queryVO.getTimeGranularity(),
                queryVO.getStartTime(),
                queryVO.getEndTime(),
                queryVO.getChannelNos(),
                queryVO.getProductNos(),
                queryVO.getOperationTypes(),
                queryVO.getIncludeSuccessData()
        );

        return buildTrendAnalysisResult(queryVO.getTimeGranularity(), rawData);
    }

    @Override
    public StatisticsResultVO getCrossAnalysis(StatisticsQueryVO queryVO) {
        log.info("执行多维度交叉分析，分组字段：{}", queryVO.getGroupFields());
        queryVO.setDimension("CROSS");
        return getMultiDimensionStatistics(queryVO);
    }

    @Override
    public StatisticsResultVO getOperationalMonitoring(StatisticsQueryVO queryVO) {
        log.info("执行运营数据监控");
        
        // 按服务器维度统计
        StatisticsQueryVO serverQuery = copyQueryVO(queryVO);
        serverQuery.setDimension("SERVER");
        StatisticsResultVO serverStats = getMultiDimensionStatistics(serverQuery);
        
        // 按平台维度统计
        StatisticsQueryVO platformQuery = copyQueryVO(queryVO);
        platformQuery.setDimension("PLATFORM");
        StatisticsResultVO platformStats = getMultiDimensionStatistics(platformQuery);
        
        // 合并结果
        List<StatisticsResultVO.StatisticsItemVO> allItems = new ArrayList<>();
        if (serverStats.getItems() != null) {
            allItems.addAll(serverStats.getItems());
        }
        if (platformStats.getItems() != null) {
            allItems.addAll(platformStats.getItems());
        }
        
        return StatisticsResultVO.builder()
                .dimension("OPERATIONAL")
                .items(allItems)
                .summary(calculateSummary(allItems))
                .statisticsTime(LocalDateTime.now())
                .build();
    }

    @Override
    public RankingVO getRanking(StatisticsQueryVO queryVO, String rankingType, String sortMetric, Integer topN) {
        log.info("获取排行榜，类型：{}，排序指标：{}，前{}名", rankingType, sortMetric, topN);
        
        if (topN == null || topN <= 0) {
            topN = 10;
        }
        
        List<Map<String, Object>> rawData = apiOrderMapper.getRankingStatistics(
                rankingType,
                sortMetric,
                queryVO.getStartTime(),
                queryVO.getEndTime(),
                queryVO.getChannelNos(),
                queryVO.getProductNos(),
                queryVO.getOperationTypes(),
                queryVO.getPlatforms(),
                topN,
                queryVO.getIncludeSuccessData()
        );

        return buildRankingResult(rankingType, sortMetric, rawData);
    }

    @Override
    public Map<String, Object> getRealtimeOverview(StatisticsQueryVO queryVO) {
        log.info("获取实时统计概览");
        
        Map<String, Object> overview = apiOrderMapper.getRealtimeOverview(
                queryVO.getStartTime(),
                queryVO.getEndTime()
        );
        
        // 添加时间信息
        overview.put("statisticsTime", LocalDateTime.now().format(DATE_TIME_FORMATTER));
        overview.put("timeRange", formatTimeRange(queryVO.getStartTime(), queryVO.getEndTime()));
        
        return overview;
    }

    @Override
    public Map<String, Object> getConversionFunnel(StatisticsQueryVO queryVO) {
        log.info("获取转化漏斗分析");
        
        Map<String, Object> funnelData = apiOrderMapper.getConversionFunnelStatistics(
                queryVO.getStartTime(),
                queryVO.getEndTime(),
                queryVO.getChannelNos(),
                queryVO.getProductNos()
        );
        
        return funnelData;
    }

    @Override
    public Map<String, Object> getUserBehaviorAnalysis(StatisticsQueryVO queryVO) {
        log.info("获取用户行为分析");
        
        Map<String, Object> behaviorData = new HashMap<>();
        
        // 用户操作类型分布
        StatisticsQueryVO operationQuery = copyQueryVO(queryVO);
        operationQuery.setDimension("OPERATION");
        StatisticsResultVO operationStats = getMultiDimensionStatistics(operationQuery);
        behaviorData.put("operationDistribution", operationStats.getItems());
        
        // 用户平台分布
        StatisticsQueryVO platformQuery = copyQueryVO(queryVO);
        platformQuery.setDimension("PLATFORM");
        StatisticsResultVO platformStats = getMultiDimensionStatistics(platformQuery);
        behaviorData.put("platformDistribution", platformStats.getItems());
        
        return behaviorData;
    }

    @Override
    public Map<String, Object> getAnomalyMonitoring(StatisticsQueryVO queryVO) {
        log.info("获取异常数据监控");
        
        List<Map<String, Object>> anomalies = apiOrderMapper.getAnomalyStatistics(
                queryVO.getStartTime(),
                queryVO.getEndTime()
        );
        
        Map<String, Object> result = new HashMap<>();
        result.put("anomalies", anomalies);
        result.put("totalAnomalies", anomalies.size());
        result.put("statisticsTime", LocalDateTime.now().format(DATE_TIME_FORMATTER));
        
        return result;
    }

    @Override
    public String exportStatisticsData(StatisticsQueryVO queryVO, String exportFormat) {
        log.info("导出统计数据，格式：{}", exportFormat);
        
        // TODO: 实现数据导出功能
        // 这里可以集成 EasyExcel 或其他导出工具
        
        return "export_" + System.currentTimeMillis() + "." + exportFormat.toLowerCase();
    }

    @Override
    public StatisticsResultVO getStatisticsWithCache(String cacheKey, StatisticsQueryVO queryVO, Integer cacheMinutes) {
        String fullCacheKey = CACHE_PREFIX + cacheKey;
        
        // 尝试从缓存获取
        StatisticsResultVO cachedResult = RedisUtils.getForEntity(fullCacheKey, StatisticsResultVO.class);
        if (cachedResult != null) {
            log.info("从缓存获取统计数据，缓存键：{}", fullCacheKey);
            return cachedResult;
        }
        
        // 缓存不存在，执行查询
        StatisticsResultVO result = getMultiDimensionStatistics(queryVO);
        
        // 缓存结果
        if (cacheMinutes == null || cacheMinutes <= 0) {
            cacheMinutes = 30; // 默认缓存30分钟
        }
        RedisUtils.set(fullCacheKey, result, cacheMinutes * 60);
        
        log.info("统计数据已缓存，缓存键：{}，缓存时间：{}分钟", fullCacheKey, cacheMinutes);
        return result;
    }

    @Override
    public void clearStatisticsCache(String cacheKey) {
        if (StrUtil.isBlank(cacheKey)) {
            // 清除所有统计缓存
            RedisUtils.deleteByPattern(CACHE_PREFIX + "*");
            log.info("已清除所有统计缓存");
        } else {
            String fullCacheKey = CACHE_PREFIX + cacheKey;
            RedisUtils.del(fullCacheKey);
            log.info("已清除统计缓存，缓存键：{}", fullCacheKey);
        }
    }

    /**
     * 构建统计结果
     */
    private StatisticsResultVO buildStatisticsResult(String dimension, List<Map<String, Object>> rawData) {
        if (CollUtil.isEmpty(rawData)) {
            return StatisticsResultVO.builder()
                    .dimension(dimension)
                    .items(new ArrayList<>())
                    .summary(StatisticsResultVO.StatisticsSummaryVO.builder().build())
                    .statisticsTime(LocalDateTime.now())
                    .build();
        }

        List<StatisticsResultVO.StatisticsItemVO> items = rawData.stream()
                .map(this::convertToStatisticsItem)
                .collect(Collectors.toList());

        return StatisticsResultVO.builder()
                .dimension(dimension)
                .items(items)
                .summary(calculateSummary(items))
                .statisticsTime(LocalDateTime.now())
                .build();
    }

    /**
     * 转换为统计项
     */
    private StatisticsResultVO.StatisticsItemVO convertToStatisticsItem(Map<String, Object> data) {
        return StatisticsResultVO.StatisticsItemVO.builder()
                .dimensionValue(getStringValue(data, "dimensionValue"))
                .dimensionName(getStringValue(data, "dimensionName"))
                .totalCount(getLongValue(data, "totalCount"))
                .smsCount(getLongValue(data, "smsCount"))
                .orderCount(getLongValue(data, "orderCount"))
                .successCount(getLongValue(data, "successCount"))
                .distinctUserCount(getLongValue(data, "distinctUserCount"))
                .submitRate(getBigDecimalValue(data, "submitRate"))
                .successRate(calculateSuccessRate(getLongValue(data, "successCount"), getLongValue(data, "orderCount")))
                .conversionRate(calculateConversionRate(getLongValue(data, "successCount"), getLongValue(data, "smsCount")))
                .build();
    }

    /**
     * 计算汇总数据
     */
    private StatisticsResultVO.StatisticsSummaryVO calculateSummary(List<StatisticsResultVO.StatisticsItemVO> items) {
        if (CollUtil.isEmpty(items)) {
            return StatisticsResultVO.StatisticsSummaryVO.builder().build();
        }

        long totalSmsCount = items.stream().mapToLong(item -> item.getSmsCount() != null ? item.getSmsCount() : 0).sum();
        long totalOrderCount = items.stream().mapToLong(item -> item.getOrderCount() != null ? item.getOrderCount() : 0).sum();
        long totalSuccessCount = items.stream().mapToLong(item -> item.getSuccessCount() != null ? item.getSuccessCount() : 0).sum();

        return StatisticsResultVO.StatisticsSummaryVO.builder()
                .totalRecords((long) items.size())
                .totalSmsCount(totalSmsCount)
                .totalOrderCount(totalOrderCount)
                .totalSuccessCount(totalSuccessCount)
                .avgSubmitRate(calculateAvgSubmitRate(totalOrderCount, totalSmsCount))
                .avgSuccessRate(calculateAvgSuccessRate(totalSuccessCount, totalOrderCount))
                .avgConversionRate(calculateAvgConversionRate(totalSuccessCount, totalSmsCount))
                .dimensionCount(items.size())
                .build();
    }

    /**
     * 构建趋势分析结果
     */
    private TrendAnalysisVO buildTrendAnalysisResult(String timeGranularity, List<Map<String, Object>> rawData) {
        // TODO: 实现趋势分析结果构建
        return TrendAnalysisVO.builder()
                .timeGranularity(timeGranularity)
                .dataPoints(new ArrayList<>())
                .summary(TrendAnalysisVO.TrendSummaryVO.builder().build())
                .build();
    }

    /**
     * 构建排行榜结果
     */
    private RankingVO buildRankingResult(String rankingType, String sortMetric, List<Map<String, Object>> rawData) {
        // TODO: 实现排行榜结果构建
        return RankingVO.builder()
                .rankingType(rankingType)
                .sortMetric(sortMetric)
                .rankings(new ArrayList<>())
                .summary(RankingVO.RankingSummaryVO.builder().build())
                .build();
    }

    // 辅助方法
    private StatisticsQueryVO copyQueryVO(StatisticsQueryVO original) {
        return StatisticsQueryVO.builder()
                .startTime(original.getStartTime())
                .endTime(original.getEndTime())
                .channelNos(original.getChannelNos())
                .productNos(original.getProductNos())
                .operationTypes(original.getOperationTypes())
                .platforms(original.getPlatforms())
                .includeSuccessData(original.getIncludeSuccessData())
                .build();
    }

    private String getStringValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        return value != null ? value.toString() : null;
    }

    private Long getLongValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value == null) return 0L;
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            return 0L;
        }
    }

    private BigDecimal getBigDecimalValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value == null) return BigDecimal.ZERO;
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }

    private BigDecimal calculateSuccessRate(Long successCount, Long orderCount) {
        if (orderCount == null || orderCount == 0) return BigDecimal.ZERO;
        if (successCount == null) successCount = 0L;
        return BigDecimal.valueOf(successCount)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(orderCount), 2, RoundingMode.HALF_UP);
    }

    private BigDecimal calculateConversionRate(Long successCount, Long smsCount) {
        if (smsCount == null || smsCount == 0) return BigDecimal.ZERO;
        if (successCount == null) successCount = 0L;
        return BigDecimal.valueOf(successCount)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(smsCount), 2, RoundingMode.HALF_UP);
    }

    private BigDecimal calculateAvgSubmitRate(Long totalOrderCount, Long totalSmsCount) {
        if (totalSmsCount == 0) return BigDecimal.ZERO;
        return BigDecimal.valueOf(totalOrderCount)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(totalSmsCount), 2, RoundingMode.HALF_UP);
    }

    private BigDecimal calculateAvgSuccessRate(Long totalSuccessCount, Long totalOrderCount) {
        if (totalOrderCount == 0) return BigDecimal.ZERO;
        return BigDecimal.valueOf(totalSuccessCount)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(totalOrderCount), 2, RoundingMode.HALF_UP);
    }

    private BigDecimal calculateAvgConversionRate(Long totalSuccessCount, Long totalSmsCount) {
        if (totalSmsCount == 0) return BigDecimal.ZERO;
        return BigDecimal.valueOf(totalSuccessCount)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(totalSmsCount), 2, RoundingMode.HALF_UP);
    }

    private String formatTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null && endTime == null) return "全部时间";
        if (startTime == null) return "截止到 " + endTime.format(DATE_TIME_FORMATTER);
        if (endTime == null) return "从 " + startTime.format(DATE_TIME_FORMATTER) + " 开始";
        return startTime.format(DATE_TIME_FORMATTER) + " ~ " + endTime.format(DATE_TIME_FORMATTER);
    }
}
