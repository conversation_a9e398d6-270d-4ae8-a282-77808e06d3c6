package com.xy.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xy.admin.entity.ApiOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @since 2025/7/30 15:39
 */
@Mapper
public interface ApiOrderMapper extends BaseMapper<ApiOrder> {

    /**
     * 多维度统计查询
     *
     * @param dimension 统计维度
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appIds 应用ID列表
     * @param channelNos 渠道编号列表
     * @param productNos 产品编号列表
     * @param operationTypes 操作类型列表
     * @param platforms 平台列表
     * @param includeSuccessData 是否包含成功数据
     * @param groupFields 分组字段列表
     * @param sortField 排序字段
     * @param sortDirection 排序方向
     * @return 统计结果
     */
    List<Map<String, Object>> getMultiDimensionStatistics(
            @Param("dimension") String dimension,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("appIds") List<String> appIds,
            @Param("channelNos") List<String> channelNos,
            @Param("productNos") List<String> productNos,
            @Param("operationTypes") List<String> operationTypes,
            @Param("platforms") List<String> platforms,
            @Param("appPackages") List<String> appPackages,
            @Param("appNames") List<String> appNames,
            @Param("servers") List<String> servers,
            @Param("orderStatuses") List<String> orderStatuses,
            @Param("outOrderStatuses") List<String> outOrderStatuses,
            @Param("includeSuccessData") Boolean includeSuccessData,
            @Param("groupFields") List<String> groupFields,
            @Param("sortField") String sortField,
            @Param("sortDirection") String sortDirection,
            @Param("distinctUsers") Boolean distinctUsers,
            @Param("minCount") Integer minCount
    );

    /**
     * 时间趋势统计查询
     *
     * @param timeGranularity 时间粒度：DAY-日，WEEK-周，MONTH-月
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param channelNos 渠道编号列表
     * @param productNos 产品编号列表
     * @param includeSuccessData 是否包含成功数据
     * @return 趋势统计结果
     */
    List<Map<String, Object>> getTimeTrendStatistics(
            @Param("timeGranularity") String timeGranularity,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("channelNos") List<String> channelNos,
            @Param("productNos") List<String> productNos,
            @Param("operationTypes") List<String> operationTypes,
            @Param("includeSuccessData") Boolean includeSuccessData
    );

    /**
     * 排行榜统计查询
     *
     * @param rankingType 排行榜类型
     * @param sortMetric 排序指标
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param channelNos 渠道编号列表
     * @param productNos 产品编号列表
     * @param topN 取前N名
     * @param includeSuccessData 是否包含成功数据
     * @return 排行榜结果
     */
    List<Map<String, Object>> getRankingStatistics(
            @Param("rankingType") String rankingType,
            @Param("sortMetric") String sortMetric,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("channelNos") List<String> channelNos,
            @Param("productNos") List<String> productNos,
            @Param("operationTypes") List<String> operationTypes,
            @Param("platforms") List<String> platforms,
            @Param("topN") Integer topN,
            @Param("includeSuccessData") Boolean includeSuccessData
    );

    /**
     * 实时统计概览
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 实时统计概览
     */
    Map<String, Object> getRealtimeOverview(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 转化漏斗统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param channelNos 渠道编号列表
     * @param productNos 产品编号列表
     * @return 转化漏斗数据
     */
    Map<String, Object> getConversionFunnelStatistics(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("channelNos") List<String> channelNos,
            @Param("productNos") List<String> productNos
    );

    /**
     * 异常数据监控
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 异常数据统计
     */
    List<Map<String, Object>> getAnomalyStatistics(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );
}