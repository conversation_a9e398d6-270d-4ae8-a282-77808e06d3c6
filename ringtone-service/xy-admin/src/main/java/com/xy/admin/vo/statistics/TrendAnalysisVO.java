package com.xy.admin.vo.statistics;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 趋势分析VO
 * 
 * <AUTHOR>
 * @since 2025/8/7
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TrendAnalysisVO {

    /**
     * 时间粒度：DAY-日，WEEK-周，MONTH-月
     */
    private String timeGranularity;

    /**
     * 趋势数据列表
     */
    private List<TrendDataPointVO> dataPoints;

    /**
     * 趋势汇总信息
     */
    private TrendSummaryVO summary;

    /**
     * 趋势数据点VO
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TrendDataPointVO {
        
        /**
         * 统计日期
         */
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate date;

        /**
         * 时间标签（用于显示）
         */
        private String timeLabel;

        /**
         * 总数
         */
        private Long totalCount;

        /**
         * 短信发送数
         */
        private Long smsCount;

        /**
         * 订单提交数
         */
        private Long orderCount;

        /**
         * 成功数
         */
        private Long successCount;

        /**
         * 去重用户数
         */
        private Long distinctUserCount;

        /**
         * 提交率
         */
        private BigDecimal submitRate;

        /**
         * 成功率
         */
        private BigDecimal successRate;

        /**
         * 转化率
         */
        private BigDecimal conversionRate;

        /**
         * 环比增长率（相比前一时间点）
         */
        private BigDecimal growthRate;

        /**
         * 同比增长率（相比去年同期）
         */
        private BigDecimal yearOverYearGrowthRate;
    }

    /**
     * 趋势汇总VO
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TrendSummaryVO {
        
        /**
         * 统计开始时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate startDate;

        /**
         * 统计结束时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate endDate;

        /**
         * 数据点数量
         */
        private Integer dataPointCount;

        /**
         * 总体趋势：UP-上升，DOWN-下降，STABLE-稳定
         */
        private String overallTrend;

        /**
         * 最高值日期
         */
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate peakDate;

        /**
         * 最高值
         */
        private Long peakValue;

        /**
         * 最低值日期
         */
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate lowDate;

        /**
         * 最低值
         */
        private Long lowValue;

        /**
         * 平均值
         */
        private BigDecimal averageValue;

        /**
         * 总增长率（期末相比期初）
         */
        private BigDecimal totalGrowthRate;

        /**
         * 平均日增长率
         */
        private BigDecimal avgDailyGrowthRate;
    }
}
