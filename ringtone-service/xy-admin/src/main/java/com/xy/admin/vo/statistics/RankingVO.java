package com.xy.admin.vo.statistics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 排行榜VO
 * 
 * <AUTHOR>
 * @since 2025/8/7
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RankingVO {

    /**
     * 排行榜类型：CHANNEL-渠道，PRODUCT-产品，APP-应用，PLATFORM-平台
     */
    private String rankingType;

    /**
     * 排序指标：TOTAL-总数，SMS-短信数，ORDER-订单数，SUCCESS-成功数，SUBMIT_RATE-提交率，SUCCESS_RATE-成功率
     */
    private String sortMetric;

    /**
     * 排行榜数据列表
     */
    private List<RankingItemVO> rankings;

    /**
     * 排行榜汇总信息
     */
    private RankingSummaryVO summary;

    /**
     * 排行榜项VO
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RankingItemVO {
        
        /**
         * 排名
         */
        private Integer rank;

        /**
         * 维度值（如渠道编号、产品编号等）
         */
        private String dimensionValue;

        /**
         * 维度名称（如渠道名称、产品名称等）
         */
        private String dimensionName;

        /**
         * 总数
         */
        private Long totalCount;

        /**
         * 短信发送数
         */
        private Long smsCount;

        /**
         * 订单提交数
         */
        private Long orderCount;

        /**
         * 成功数
         */
        private Long successCount;

        /**
         * 去重用户数
         */
        private Long distinctUserCount;

        /**
         * 提交率
         */
        private BigDecimal submitRate;

        /**
         * 成功率
         */
        private BigDecimal successRate;

        /**
         * 转化率
         */
        private BigDecimal conversionRate;

        /**
         * 占比（在总体中的占比）
         */
        private BigDecimal percentage;

        /**
         * 排序值（用于排序的具体数值）
         */
        private BigDecimal sortValue;

        /**
         * 相比上一名的差距
         */
        private BigDecimal gapToPrevious;

        /**
         * 相比第一名的差距
         */
        private BigDecimal gapToFirst;
    }

    /**
     * 排行榜汇总VO
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RankingSummaryVO {
        
        /**
         * 总排名数量
         */
        private Integer totalRankings;

        /**
         * 第一名的值
         */
        private BigDecimal firstPlaceValue;

        /**
         * 最后一名的值
         */
        private BigDecimal lastPlaceValue;

        /**
         * 平均值
         */
        private BigDecimal averageValue;

        /**
         * 中位数
         */
        private BigDecimal medianValue;

        /**
         * 前三名总占比
         */
        private BigDecimal topThreePercentage;

        /**
         * 前十名总占比
         */
        private BigDecimal topTenPercentage;

        /**
         * 最大差距（第一名与最后一名的差距）
         */
        private BigDecimal maxGap;

        /**
         * 集中度（前20%的占比）
         */
        private BigDecimal concentrationRatio;
    }
}
