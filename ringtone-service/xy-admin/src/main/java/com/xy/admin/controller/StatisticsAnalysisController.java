package com.xy.admin.controller;

import com.xy.admin.annotation.DataSource;
import com.xy.admin.service.StatisticsAnalysisService;
import com.xy.admin.vo.statistics.RankingVO;
import com.xy.admin.vo.statistics.StatisticsQueryVO;
import com.xy.admin.vo.statistics.StatisticsResultVO;
import com.xy.admin.vo.statistics.TrendAnalysisVO;
import com.xy.base.core.response.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
 * 统计分析控制器
 * 提供多维度的可视化分析接口
 * 
 * <AUTHOR>
 * @since 2025/8/7
 */
@Slf4j
@RestController
@RequestMapping("/statistics")
@RequiredArgsConstructor
@Api(tags = "统计分析接口")
public class StatisticsAnalysisController {

    private final StatisticsAnalysisService statisticsAnalysisService;

    /**
     * 多维度统计分析
     * 支持按渠道、产品、时间、应用、平台等维度进行统计
     */
    @PostMapping("/multi-dimension")
    @DataSource("tertiary")
    @ApiOperation("多维度统计分析")
    public Result<StatisticsResultVO> getMultiDimensionStatistics(
            @Valid @RequestBody StatisticsQueryVO queryVO) {
        log.info("多维度统计分析请求，维度：{}", queryVO.getDimension());
        StatisticsResultVO result = statisticsAnalysisService.getMultiDimensionStatistics(queryVO);
        return Result.success(result);
    }

    /**
     * 渠道效果分析
     * 按渠道分组统计订单办理成功量和用户数
     */
    @PostMapping("/channel-effect")
    @DataSource("tertiary")
    @ApiOperation("渠道效果分析")
    public Result<StatisticsResultVO> getChannelEffectAnalysis(
            @Valid @RequestBody StatisticsQueryVO queryVO) {
        log.info("渠道效果分析请求");
        StatisticsResultVO result = statisticsAnalysisService.getChannelEffectAnalysis(queryVO);
        return Result.success(result);
    }

    /**
     * 产品表现分析
     * 按产品分组查看各产品的订单情况，分短信发送、订单提交
     */
    @PostMapping("/product-performance")
    @DataSource("tertiary")
    @ApiOperation("产品表现分析")
    public Result<StatisticsResultVO> getProductPerformanceAnalysis(
            @Valid @RequestBody StatisticsQueryVO queryVO) {
        log.info("产品表现分析请求");
        StatisticsResultVO result = statisticsAnalysisService.getProductPerformanceAnalysis(queryVO);
        return Result.success(result);
    }

    /**
     * 时间趋势分析
     * 按日期分组查看订单趋势
     */
    @PostMapping("/time-trend")
    @DataSource("tertiary")
    @ApiOperation("时间趋势分析")
    public Result<TrendAnalysisVO> getTimeTrendAnalysis(
            @Valid @RequestBody StatisticsQueryVO queryVO) {
        log.info("时间趋势分析请求，时间粒度：{}", queryVO.getTimeGranularity());
        TrendAnalysisVO result = statisticsAnalysisService.getTimeTrendAnalysis(queryVO);
        return Result.success(result);
    }

    /**
     * 多维度交叉分析
     * 组合多个字段进行深度分析
     */
    @PostMapping("/cross-analysis")
    @DataSource("tertiary")
    @ApiOperation("多维度交叉分析")
    public Result<StatisticsResultVO> getCrossAnalysis(
            @Valid @RequestBody StatisticsQueryVO queryVO) {
        log.info("多维度交叉分析请求，分组字段：{}", queryVO.getGroupFields());
        StatisticsResultVO result = statisticsAnalysisService.getCrossAnalysis(queryVO);
        return Result.success(result);
    }

    /**
     * 运营数据监控
     * 按服务器、平台、响应等维度监控系统运行状况
     */
    @PostMapping("/operational-monitoring")
    @DataSource("tertiary")
    @ApiOperation("运营数据监控")
    public Result<StatisticsResultVO> getOperationalMonitoring(
            @Valid @RequestBody StatisticsQueryVO queryVO) {
        log.info("运营数据监控请求");
        StatisticsResultVO result = statisticsAnalysisService.getOperationalMonitoring(queryVO);
        return Result.success(result);
    }

    /**
     * 获取排行榜
     * 支持渠道、产品、应用、平台等维度的排行
     */
    @PostMapping("/ranking")
    @DataSource("tertiary")
    @ApiOperation("获取排行榜")
    public Result<RankingVO> getRanking(
            @Valid @RequestBody StatisticsQueryVO queryVO,
            @ApiParam("排行榜类型：CHANNEL-渠道，PRODUCT-产品，APP-应用，PLATFORM-平台") 
            @RequestParam String rankingType,
            @ApiParam("排序指标：TOTAL-总数，SMS-短信数，ORDER-订单数，SUCCESS-成功数，SUBMIT_RATE-提交率，SUCCESS_RATE-成功率") 
            @RequestParam String sortMetric,
            @ApiParam("取前N名，默认10") 
            @RequestParam(defaultValue = "10") Integer topN) {
        log.info("获取排行榜请求，类型：{}，排序指标：{}，前{}名", rankingType, sortMetric, topN);
        RankingVO result = statisticsAnalysisService.getRanking(queryVO, rankingType, sortMetric, topN);
        return Result.success(result);
    }

    /**
     * 获取实时统计概览
     * 提供关键指标的实时统计数据
     */
    @PostMapping("/realtime-overview")
    @DataSource("tertiary")
    @ApiOperation("获取实时统计概览")
    public Result<Map<String, Object>> getRealtimeOverview(
            @Valid @RequestBody StatisticsQueryVO queryVO) {
        log.info("获取实时统计概览请求");
        Map<String, Object> result = statisticsAnalysisService.getRealtimeOverview(queryVO);
        return Result.success(result);
    }

    /**
     * 获取转化漏斗分析
     * 分析从短信发送到订单提交到办理成功的转化情况
     */
    @PostMapping("/conversion-funnel")
    @DataSource("tertiary")
    @ApiOperation("获取转化漏斗分析")
    public Result<Map<String, Object>> getConversionFunnel(
            @Valid @RequestBody StatisticsQueryVO queryVO) {
        log.info("获取转化漏斗分析请求");
        Map<String, Object> result = statisticsAnalysisService.getConversionFunnel(queryVO);
        return Result.success(result);
    }

    /**
     * 获取用户行为分析
     * 分析用户在不同阶段的行为特征
     */
    @PostMapping("/user-behavior")
    @DataSource("tertiary")
    @ApiOperation("获取用户行为分析")
    public Result<Map<String, Object>> getUserBehaviorAnalysis(
            @Valid @RequestBody StatisticsQueryVO queryVO) {
        log.info("获取用户行为分析请求");
        Map<String, Object> result = statisticsAnalysisService.getUserBehaviorAnalysis(queryVO);
        return Result.success(result);
    }

    /**
     * 获取异常数据监控
     * 监控异常的订单状态、响应等情况
     */
    @PostMapping("/anomaly-monitoring")
    @DataSource("tertiary")
    @ApiOperation("获取异常数据监控")
    public Result<Map<String, Object>> getAnomalyMonitoring(
            @Valid @RequestBody StatisticsQueryVO queryVO) {
        log.info("获取异常数据监控请求");
        Map<String, Object> result = statisticsAnalysisService.getAnomalyMonitoring(queryVO);
        return Result.success(result);
    }

    /**
     * 导出统计数据
     * 将统计结果导出为Excel或CSV格式
     */
    @PostMapping("/export")
    @DataSource("tertiary")
    @ApiOperation("导出统计数据")
    public Result<String> exportStatisticsData(
            @Valid @RequestBody StatisticsQueryVO queryVO,
            @ApiParam("导出格式：EXCEL, CSV") 
            @RequestParam(defaultValue = "EXCEL") String exportFormat) {
        log.info("导出统计数据请求，格式：{}", exportFormat);
        String result = statisticsAnalysisService.exportStatisticsData(queryVO, exportFormat);
        return Result.success(result);
    }

    /**
     * 获取带缓存的统计数据
     * 对于复杂的统计查询，提供缓存机制
     */
    @PostMapping("/cached")
    @DataSource("tertiary")
    @ApiOperation("获取带缓存的统计数据")
    public Result<StatisticsResultVO> getStatisticsWithCache(
            @Valid @RequestBody StatisticsQueryVO queryVO,
            @ApiParam("缓存键") 
            @RequestParam String cacheKey,
            @ApiParam("缓存时间（分钟），默认30分钟") 
            @RequestParam(defaultValue = "30") Integer cacheMinutes) {
        log.info("获取带缓存的统计数据请求，缓存键：{}，缓存时间：{}分钟", cacheKey, cacheMinutes);
        StatisticsResultVO result = statisticsAnalysisService.getStatisticsWithCache(cacheKey, queryVO, cacheMinutes);
        return Result.success(result);
    }

    /**
     * 清除统计数据缓存
     */
    @DeleteMapping("/cache")
    @ApiOperation("清除统计数据缓存")
    public Result<Void> clearStatisticsCache(
            @ApiParam("缓存键，为空时清除所有统计缓存") 
            @RequestParam(required = false) String cacheKey) {
        log.info("清除统计数据缓存请求，缓存键：{}", cacheKey);
        statisticsAnalysisService.clearStatisticsCache(cacheKey);
        return Result.success(null);
    }

    /**
     * 获取统计维度选项
     * 提供前端选择统计维度的选项
     */
    @GetMapping("/dimension-options")
    @ApiOperation("获取统计维度选项")
    public Result<Map<String, String>> getDimensionOptions() {
        Map<String, String> options = Map.of(
                "CHANNEL", "渠道维度",
                "PRODUCT", "产品维度",
                "TIME", "时间维度",
                "APP", "应用维度",
                "PLATFORM", "平台维度",
                "OPERATION", "操作类型维度",
                "CROSS", "交叉分析维度"
        );
        return Result.success(options);
    }

    /**
     * 获取时间粒度选项
     */
    @GetMapping("/time-granularity-options")
    @ApiOperation("获取时间粒度选项")
    public Result<Map<String, String>> getTimeGranularityOptions() {
        Map<String, String> options = Map.of(
                "DAY", "按日统计",
                "WEEK", "按周统计",
                "MONTH", "按月统计"
        );
        return Result.success(options);
    }

    /**
     * 获取排序指标选项
     */
    @GetMapping("/sort-metric-options")
    @ApiOperation("获取排序指标选项")
    public Result<Map<String, String>> getSortMetricOptions() {
        Map<String, String> options = Map.of(
                "TOTAL", "总数",
                "SMS", "短信数",
                "ORDER", "订单数",
                "SUCCESS", "成功数",
                "SUBMIT_RATE", "提交率",
                "SUCCESS_RATE", "成功率"
        );
        return Result.success(options);
    }
}
