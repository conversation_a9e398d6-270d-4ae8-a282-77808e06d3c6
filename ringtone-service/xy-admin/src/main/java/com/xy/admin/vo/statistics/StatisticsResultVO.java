package com.xy.admin.vo.statistics;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 统计分析结果VO
 * 
 * <AUTHOR>
 * @since 2025/8/7
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StatisticsResultVO {

    /**
     * 统计维度
     */
    private String dimension;

    /**
     * 统计数据列表
     */
    private List<StatisticsItemVO> items;

    /**
     * 汇总数据
     */
    private StatisticsSummaryVO summary;

    /**
     * 统计时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime statisticsTime;

    /**
     * 统计项VO
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class StatisticsItemVO {
        
        /**
         * 维度值（如渠道编号、产品编号等）
         */
        private String dimensionValue;

        /**
         * 维度名称（如渠道名称、产品名称等）
         */
        private String dimensionName;

        /**
         * 统计日期（按时间维度统计时使用）
         */
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate statisticsDate;

        /**
         * 总数
         */
        private Long totalCount;

        /**
         * 短信发送数（SMS）
         */
        private Long smsCount;

        /**
         * 订单提交数（ORDER）
         */
        private Long orderCount;

        /**
         * 成功数（关联pro_order表统计）
         */
        private Long successCount;

        /**
         * 去重用户数
         */
        private Long distinctUserCount;

        /**
         * 提交率（订单提交数/短信发送数）
         */
        private BigDecimal submitRate;

        /**
         * 成功率（成功数/订单提交数）
         */
        private BigDecimal successRate;

        /**
         * 失败率（1-成功率）
         */
        private BigDecimal failureRate;

        /**
         * 转化率（成功数/短信发送数）
         */
        private BigDecimal conversionRate;

        /**
         * 扩展属性（用于存储额外的统计信息）
         */
        private Map<String, Object> extraData;

        /**
         * 排名（在同维度中的排名）
         */
        private Integer rank;
    }

    /**
     * 统计汇总VO
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class StatisticsSummaryVO {
        
        /**
         * 总记录数
         */
        private Long totalRecords;

        /**
         * 总短信发送数
         */
        private Long totalSmsCount;

        /**
         * 总订单提交数
         */
        private Long totalOrderCount;

        /**
         * 总成功数
         */
        private Long totalSuccessCount;

        /**
         * 总去重用户数
         */
        private Long totalDistinctUserCount;

        /**
         * 平均提交率
         */
        private BigDecimal avgSubmitRate;

        /**
         * 平均成功率
         */
        private BigDecimal avgSuccessRate;

        /**
         * 平均转化率
         */
        private BigDecimal avgConversionRate;

        /**
         * 最高提交率
         */
        private BigDecimal maxSubmitRate;

        /**
         * 最低提交率
         */
        private BigDecimal minSubmitRate;

        /**
         * 最高成功率
         */
        private BigDecimal maxSuccessRate;

        /**
         * 最低成功率
         */
        private BigDecimal minSuccessRate;

        /**
         * 统计时间范围
         */
        private String timeRange;

        /**
         * 统计维度数量
         */
        private Integer dimensionCount;
    }
}
