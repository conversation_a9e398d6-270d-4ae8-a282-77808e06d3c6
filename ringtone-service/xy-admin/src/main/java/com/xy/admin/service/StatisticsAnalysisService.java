package com.xy.admin.service;

import com.xy.admin.vo.statistics.RankingVO;
import com.xy.admin.vo.statistics.StatisticsQueryVO;
import com.xy.admin.vo.statistics.StatisticsResultVO;
import com.xy.admin.vo.statistics.TrendAnalysisVO;

import java.util.List;
import java.util.Map;

/**
 * 统计分析服务接口
 * 
 * <AUTHOR>
 * @since 2025/8/7
 */
public interface StatisticsAnalysisService {

    /**
     * 多维度统计分析
     * 支持按渠道、产品、时间、应用、平台等维度进行统计
     * 
     * @param queryVO 查询条件
     * @return 统计结果
     */
    StatisticsResultVO getMultiDimensionStatistics(StatisticsQueryVO queryVO);

    /**
     * 渠道效果分析
     * 按渠道分组统计订单办理成功量和用户数
     * 
     * @param queryVO 查询条件
     * @return 渠道统计结果
     */
    StatisticsResultVO getChannelEffectAnalysis(StatisticsQueryVO queryVO);

    /**
     * 产品表现分析
     * 按产品分组查看各产品的订单情况，分短信发送、订单提交
     * 
     * @param queryVO 查询条件
     * @return 产品统计结果
     */
    StatisticsResultVO getProductPerformanceAnalysis(StatisticsQueryVO queryVO);

    /**
     * 时间趋势分析
     * 按日期分组查看订单趋势
     * 
     * @param queryVO 查询条件
     * @return 趋势分析结果
     */
    TrendAnalysisVO getTimeTrendAnalysis(StatisticsQueryVO queryVO);

    /**
     * 多维度交叉分析
     * 组合多个字段进行深度分析
     * 
     * @param queryVO 查询条件
     * @return 交叉分析结果
     */
    StatisticsResultVO getCrossAnalysis(StatisticsQueryVO queryVO);

    /**
     * 运营数据监控
     * 按服务器、平台、响应等维度监控系统运行状况
     * 
     * @param queryVO 查询条件
     * @return 运营监控结果
     */
    StatisticsResultVO getOperationalMonitoring(StatisticsQueryVO queryVO);

    /**
     * 获取排行榜
     * 支持渠道、产品、应用、平台等维度的排行
     * 
     * @param queryVO 查询条件
     * @param rankingType 排行榜类型：CHANNEL-渠道，PRODUCT-产品，APP-应用，PLATFORM-平台
     * @param sortMetric 排序指标：TOTAL-总数，SMS-短信数，ORDER-订单数，SUCCESS-成功数，SUBMIT_RATE-提交率，SUCCESS_RATE-成功率
     * @param topN 取前N名，默认10
     * @return 排行榜结果
     */
    RankingVO getRanking(StatisticsQueryVO queryVO, String rankingType, String sortMetric, Integer topN);

    /**
     * 获取实时统计概览
     * 提供关键指标的实时统计数据
     * 
     * @param queryVO 查询条件
     * @return 实时统计概览
     */
    Map<String, Object> getRealtimeOverview(StatisticsQueryVO queryVO);

    /**
     * 获取转化漏斗分析
     * 分析从短信发送到订单提交到办理成功的转化情况
     * 
     * @param queryVO 查询条件
     * @return 转化漏斗数据
     */
    Map<String, Object> getConversionFunnel(StatisticsQueryVO queryVO);

    /**
     * 获取用户行为分析
     * 分析用户在不同阶段的行为特征
     * 
     * @param queryVO 查询条件
     * @return 用户行为分析结果
     */
    Map<String, Object> getUserBehaviorAnalysis(StatisticsQueryVO queryVO);

    /**
     * 获取异常数据监控
     * 监控异常的订单状态、响应等情况
     * 
     * @param queryVO 查询条件
     * @return 异常数据监控结果
     */
    Map<String, Object> getAnomalyMonitoring(StatisticsQueryVO queryVO);

    /**
     * 导出统计数据
     * 将统计结果导出为Excel或CSV格式
     * 
     * @param queryVO 查询条件
     * @param exportFormat 导出格式：EXCEL, CSV
     * @return 导出文件路径或下载链接
     */
    String exportStatisticsData(StatisticsQueryVO queryVO, String exportFormat);

    /**
     * 获取统计数据缓存
     * 对于复杂的统计查询，提供缓存机制
     * 
     * @param cacheKey 缓存键
     * @param queryVO 查询条件
     * @param cacheMinutes 缓存时间（分钟）
     * @return 统计结果
     */
    StatisticsResultVO getStatisticsWithCache(String cacheKey, StatisticsQueryVO queryVO, Integer cacheMinutes);

    /**
     * 清除统计数据缓存
     * 
     * @param cacheKey 缓存键，为空时清除所有统计缓存
     */
    void clearStatisticsCache(String cacheKey);
}
