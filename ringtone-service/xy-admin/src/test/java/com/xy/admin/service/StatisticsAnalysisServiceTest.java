package com.xy.admin.service;

import com.xy.admin.vo.statistics.StatisticsQueryVO;
import com.xy.admin.vo.statistics.StatisticsResultVO;
import com.xy.admin.vo.statistics.TrendAnalysisVO;
import com.xy.admin.vo.statistics.RankingVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Map;

/**
 * 统计分析服务测试类
 * 
 * <AUTHOR>
 * @since 2025/8/7
 */
@SpringBootTest
@ActiveProfiles("dev-zsgl")
public class StatisticsAnalysisServiceTest {

    @Autowired
    private StatisticsAnalysisService statisticsAnalysisService;

    /**
     * 测试多维度统计分析
     */
    @Test
    public void testMultiDimensionStatistics() {
        StatisticsQueryVO queryVO = StatisticsQueryVO.builder()
                .dimension("CHANNEL")
                .startTime(LocalDateTime.now().minusDays(7))
                .endTime(LocalDateTime.now())
                .includeSuccessData(true)
                .distinctUsers(true)
                .sortField("totalCount")
                .sortDirection("DESC")
                .build();

        StatisticsResultVO result = statisticsAnalysisService.getMultiDimensionStatistics(queryVO);
        
        System.out.println("=== 多维度统计分析结果 ===");
        System.out.println("统计维度: " + result.getDimension());
        System.out.println("统计项数量: " + (result.getItems() != null ? result.getItems().size() : 0));
        
        if (result.getItems() != null && !result.getItems().isEmpty()) {
            result.getItems().forEach(item -> {
                System.out.println(String.format("维度值: %s, 总数: %d, 短信数: %d, 订单数: %d, 成功数: %d, 提交率: %s%%",
                        item.getDimensionValue(),
                        item.getTotalCount(),
                        item.getSmsCount(),
                        item.getOrderCount(),
                        item.getSuccessCount(),
                        item.getSubmitRate()));
            });
        }
        
        if (result.getSummary() != null) {
            System.out.println("=== 汇总信息 ===");
            System.out.println("总记录数: " + result.getSummary().getTotalRecords());
            System.out.println("总短信数: " + result.getSummary().getTotalSmsCount());
            System.out.println("总订单数: " + result.getSummary().getTotalOrderCount());
            System.out.println("总成功数: " + result.getSummary().getTotalSuccessCount());
            System.out.println("平均提交率: " + result.getSummary().getAvgSubmitRate() + "%");
        }
    }

    /**
     * 测试渠道效果分析
     */
    @Test
    public void testChannelEffectAnalysis() {
        StatisticsQueryVO queryVO = StatisticsQueryVO.builder()
                .startTime(LocalDateTime.now().minusDays(30))
                .endTime(LocalDateTime.now())
                .includeSuccessData(true)
                .build();

        StatisticsResultVO result = statisticsAnalysisService.getChannelEffectAnalysis(queryVO);
        
        System.out.println("=== 渠道效果分析结果 ===");
        System.out.println("统计维度: " + result.getDimension());
        System.out.println("渠道数量: " + (result.getItems() != null ? result.getItems().size() : 0));
        
        if (result.getItems() != null && !result.getItems().isEmpty()) {
            result.getItems().stream()
                    .limit(5) // 只显示前5个渠道
                    .forEach(item -> {
                        System.out.println(String.format("渠道: %s, 用户数: %d, 成功数: %d, 转化率: %s%%",
                                item.getDimensionValue(),
                                item.getDistinctUserCount(),
                                item.getSuccessCount(),
                                item.getConversionRate()));
                    });
        }
    }

    /**
     * 测试产品表现分析
     */
    @Test
    public void testProductPerformanceAnalysis() {
        StatisticsQueryVO queryVO = StatisticsQueryVO.builder()
                .startTime(LocalDateTime.now().minusDays(7))
                .endTime(LocalDateTime.now())
                .includeSuccessData(true)
                .build();

        StatisticsResultVO result = statisticsAnalysisService.getProductPerformanceAnalysis(queryVO);
        
        System.out.println("=== 产品表现分析结果 ===");
        System.out.println("产品数量: " + (result.getItems() != null ? result.getItems().size() : 0));
        
        if (result.getItems() != null && !result.getItems().isEmpty()) {
            result.getItems().stream()
                    .limit(5) // 只显示前5个产品
                    .forEach(item -> {
                        System.out.println(String.format("产品: %s, 短信数: %d, 订单数: %d, 成功数: %d, 成功率: %s%%",
                                item.getDimensionValue(),
                                item.getSmsCount(),
                                item.getOrderCount(),
                                item.getSuccessCount(),
                                item.getSuccessRate()));
                    });
        }
    }

    /**
     * 测试时间趋势分析
     */
    @Test
    public void testTimeTrendAnalysis() {
        StatisticsQueryVO queryVO = StatisticsQueryVO.builder()
                .timeGranularity("DAY")
                .startTime(LocalDateTime.now().minusDays(7))
                .endTime(LocalDateTime.now())
                .includeSuccessData(true)
                .build();

        TrendAnalysisVO result = statisticsAnalysisService.getTimeTrendAnalysis(queryVO);
        
        System.out.println("=== 时间趋势分析结果 ===");
        System.out.println("时间粒度: " + result.getTimeGranularity());
        System.out.println("数据点数量: " + (result.getDataPoints() != null ? result.getDataPoints().size() : 0));
        
        if (result.getDataPoints() != null && !result.getDataPoints().isEmpty()) {
            result.getDataPoints().forEach(point -> {
                System.out.println(String.format("日期: %s, 总数: %d, 短信数: %d, 订单数: %d, 提交率: %s%%",
                        point.getTimeLabel(),
                        point.getTotalCount(),
                        point.getSmsCount(),
                        point.getOrderCount(),
                        point.getSubmitRate()));
            });
        }
    }

    /**
     * 测试排行榜
     */
    @Test
    public void testRanking() {
        StatisticsQueryVO queryVO = StatisticsQueryVO.builder()
                .startTime(LocalDateTime.now().minusDays(30))
                .endTime(LocalDateTime.now())
                .includeSuccessData(true)
                .build();

        RankingVO result = statisticsAnalysisService.getRanking(queryVO, "CHANNEL", "TOTAL", 10);
        
        System.out.println("=== 渠道排行榜结果 ===");
        System.out.println("排行榜类型: " + result.getRankingType());
        System.out.println("排序指标: " + result.getSortMetric());
        System.out.println("排名数量: " + (result.getRankings() != null ? result.getRankings().size() : 0));
        
        if (result.getRankings() != null && !result.getRankings().isEmpty()) {
            result.getRankings().forEach(ranking -> {
                System.out.println(String.format("排名: %d, 渠道: %s, 总数: %d, 占比: %s%%",
                        ranking.getRank(),
                        ranking.getDimensionValue(),
                        ranking.getTotalCount(),
                        ranking.getPercentage()));
            });
        }
    }

    /**
     * 测试实时统计概览
     */
    @Test
    public void testRealtimeOverview() {
        StatisticsQueryVO queryVO = StatisticsQueryVO.builder()
                .startTime(LocalDateTime.now().minusHours(1))
                .endTime(LocalDateTime.now())
                .build();

        Map<String, Object> result = statisticsAnalysisService.getRealtimeOverview(queryVO);
        
        System.out.println("=== 实时统计概览结果 ===");
        result.forEach((key, value) -> {
            System.out.println(key + ": " + value);
        });
    }

    /**
     * 测试转化漏斗分析
     */
    @Test
    public void testConversionFunnel() {
        StatisticsQueryVO queryVO = StatisticsQueryVO.builder()
                .startTime(LocalDateTime.now().minusDays(1))
                .endTime(LocalDateTime.now())
                .build();

        Map<String, Object> result = statisticsAnalysisService.getConversionFunnel(queryVO);
        
        System.out.println("=== 转化漏斗分析结果 ===");
        result.forEach((key, value) -> {
            System.out.println(key + ": " + value);
        });
    }

    /**
     * 测试异常数据监控
     */
    @Test
    public void testAnomalyMonitoring() {
        StatisticsQueryVO queryVO = StatisticsQueryVO.builder()
                .startTime(LocalDateTime.now().minusDays(1))
                .endTime(LocalDateTime.now())
                .build();

        Map<String, Object> result = statisticsAnalysisService.getAnomalyMonitoring(queryVO);
        
        System.out.println("=== 异常数据监控结果 ===");
        result.forEach((key, value) -> {
            System.out.println(key + ": " + value);
        });
    }

    /**
     * 测试缓存功能
     */
    @Test
    public void testCacheFunction() {
        StatisticsQueryVO queryVO = StatisticsQueryVO.builder()
                .dimension("CHANNEL")
                .startTime(LocalDateTime.now().minusDays(7))
                .endTime(LocalDateTime.now())
                .build();

        String cacheKey = "test_cache_" + System.currentTimeMillis();
        
        // 第一次查询（无缓存）
        long startTime1 = System.currentTimeMillis();
        StatisticsResultVO result1 = statisticsAnalysisService.getStatisticsWithCache(cacheKey, queryVO, 5);
        long endTime1 = System.currentTimeMillis();
        
        // 第二次查询（有缓存）
        long startTime2 = System.currentTimeMillis();
        StatisticsResultVO result2 = statisticsAnalysisService.getStatisticsWithCache(cacheKey, queryVO, 5);
        long endTime2 = System.currentTimeMillis();
        
        System.out.println("=== 缓存功能测试结果 ===");
        System.out.println("第一次查询耗时: " + (endTime1 - startTime1) + "ms");
        System.out.println("第二次查询耗时: " + (endTime2 - startTime2) + "ms");
        System.out.println("缓存效果: " + (endTime1 - startTime1 > endTime2 - startTime2 ? "有效" : "无效"));
        
        // 清除缓存
        statisticsAnalysisService.clearStatisticsCache(cacheKey);
        System.out.println("缓存已清除");
    }
}
